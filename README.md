# ComfyUI即梦AI插件

> 本插件旨在将将即梦AI的强大图片生成功能集成到ComfyUI中以提高生产效率。

> 使用本插件完全免费，图片和视频生成所消耗的积分为官方赠送，因此使用前请注册和登录官方账号并领取积分。

> 代码仅供个人学习和研究，如需求较大建议在即梦官网充值会员以解锁更多权益。

## 🎯 **功能特点**

- 🎨 **高质量文生图**: 基于即梦AI的先进图像生成技术
- 🎭 **多模型支持**: 支持图片3.1、3.0、2.1、2.0 Pro、2.0等多种模型
- 📐 **多比例选择**: 支持1:1、2:3、4:3、9:16、16:9等多种图像比例
- 🧩 **原生集成**: 完全集成ComfyUI工作流，支持保存和分享
- ⚡ **智能处理**: 自动处理排队、重试和错误恢复
- 🔧 **易于配置**: 简单的JSON配置文件
- 🖼️ **图片高清化**: 支持对生成的图片进行高清化处理，提升图片质量

## 📦 **安装方法**

### 方法1: 手动安装（推荐）

1. 将整个`Comfyui_Free_Jimeng`文件夹复制到ComfyUI的`custom_nodes`目录
2. 重启ComfyUI

### 方法2: Git克隆

```bash
cd ComfyUI/custom_nodes
git clone https://github.com/Lingyuzhou111/Comfyui_Free_Jimeng.git
```

## ⚙️ **配置步骤**

### 1. 获取即梦AI认证信息

1. 打开浏览器，访问 [即梦AI官网](https://jimeng.jianying.com/)
2. 登录你的账号
3. 按F12打开开发者工具
4. 切换到"Application"或"Storage"标签页
5. 在左侧展开"Cookies"，点击`https://jimeng.jianying.com`
6. 找到名为`sessionid`的cookie值（这是唯一需要的认证信息）

### 2. 配置插件

1. 在`Comfyui_Free_Jimeng`目录中找到`config.json.example`文件
2. 复制并重命名为`config.json`
3. 编辑`config.json`，填入你的认证信息：

#### 基础配置（可能遇到认证问题）
```json
{
    "accounts": [
        {
            "sessionid": "你复制的sessionid值",
            "description": "我的主账号"
        }
    ]
}
```

**⚠️ 重要：如果遇到认证错误（ret=1015），请参考 [`获取认证参数指南.md`](./获取认证参数指南.md)**

### 3. 重启ComfyUI

配置完成后，重启ComfyUI使配置生效。

## 🚀 **使用方法**

### 基础使用

1. 在ComfyUI中添加节点：`Add Node` → `即梦AI` → `即梦文生图`
2. 连接节点到你的工作流
3. 配置参数：
   - **提示词**: 输入你想要生成的图片描述
   - **模型**: 选择生成模型（2.0、2.1、2.0p、3.0、3.1）
   - **比例**: 选择图片比例（1:1、16:9等）
   - **种子**: 设置随机种子（-1为随机）
   - **生成数量**: 选择要生成的图片数量（1-4张）

### 图片高清化功能

新增的**即梦AI图片高清化**节点可以对上游即梦AI生图节点生成的图片进行高清化处理：

1. 在ComfyUI中添加节点：`Add Node` → `即梦AI` → `即梦AI图片高清化`
2. 将上游**即梦AI生图**节点的`history_id`输出连接到高清化节点的`history_id`输入
3. 从下拉框中选择`image_index`（1-4），选择要对第几张图片进行高清化处理
4. 运行工作流，节点将自动处理选定的图片

### 节点输出

#### 即梦AI生图节点
- **images**: 生成的图片（torch.Tensor格式）
- **generation_info**: 包含生成信息的文本
- **image_urls**: 原始图片URL列表（字符串格式，每行一个URL）
- **history_id**: 历史记录ID（用于下游高清化节点）

#### 即梦AI图片高清化节点
- **enhanced_image**: 高清化后的图片（torch.Tensor格式）
- **enhancement_info**: 包含高清化处理信息的文本
- **enhanced_image_url**: 高清化后图片的URL

### 示例工作流

#### 基础文生图工作流
<img width="1245" height="1141" alt="基础文生图" src="https://github.com/user-attachments/assets/ae0d1641-b445-4844-a889-11d76b4f5ff9" />


#### 文生图+高清化工作流
<img width="2170" height="1035" alt="文生图+超清放大" src="https://github.com/user-attachments/assets/4d661c95-eb78-4c35-80b7-a1f7255f3517" />


**连接说明**：
- 将即梦AI生图节点的 `history_id` 输出连接到高清化节点的 `history_id` 输入
- 从下拉框中选择要处理的图片序号（1-4）
- 高清化节点会自动获取对应的图片信息并进行处理

#### 视频工作流
<img width="985" height="1135" alt="首尾帧视频" src="https://github.com/user-attachments/assets/6a4dd414-c833-48bb-aa86-ff3258744f18" />


## 🔧 **高级配置**

### 超时设置

```json
{
    "timeout": {
        "base_timeout": 30,      // 基础请求超时时间（秒）
        "max_wait_time": 300,    // 最大等待时间（秒）
        "check_interval": 5,     // 状态检查间隔（秒）
        "max_retries": 3         // 最大重试次数
    }
}
```

### 多账号配置

```json
{
    "accounts": [
        {
            "sessionid": "账号1的sessionid",
            "description": "主账号"
        },
        {
            "sessionid": "账号2的sessionid",
            "description": "备用账号"
        }
    ]
}
```

## 📚 **API参数说明**

### 支持的模型

| 模型代码 | 显示名称 | 描述 |
|----------|----------|------|
| 3.1 | 图片 3.1 | 影视质感，文字更准，直出2k高清图 |
| 3.0 | 图片 3.0 | 影视质感，文字更准，直出2k高清图 |
| 2.1 | 图片 2.1 | 稳定的结构和更强的影视质感，支持生成中、英文文字 |
| 2.0p | 图片 2.0 Pro | 大幅提升了多样性和真实的照片质感 |
| 2.0 | 图片 2.0 | 更精准的描述词响应和多样的风格组合 |

### 支持的比例

| 比例代码 | 像素尺寸 | 描述 |
|----------|----------|------|
| 1:1 | 1328x1328 | 正方形 |
| 2:3 | 1056x1584 | 竖屏 |
| 3:4 | 1104x1472 | 标准竖屏 |
| 4:3 | 1472x1104 | 标准横屏 |
| 3:2 | 1584x1056 | 横屏 |
| 16:9 | 1664x936 | 宽屏横屏 |
| 9:16 | 936x1664 | 手机竖屏 |

## 🐛 **故障排除**

### 常见问题

1. **节点无法加载**
   - 检查插件是否正确安装在`custom_nodes`目录
   - 确保Python环境有所需的依赖包

2. **配置文件错误**
   - 检查`config.json`格式是否正确
   - 确保sessionid信息有效且未过期

3. **API调用失败**
   - 检查网络连接
   - 确认即梦AI账号状态正常
   - 检查sessionid是否过期（需要重新登录获取）
   
4. **认证错误（ret=1015）**
   - **解决方案**：参考[`获取认证参数指南.md`](./获取认证参数指南.md)获取完整认证参数

5. **图片生成失败**
   - 检查提示词是否符合规范
   - 确认账号有足够的积分
   - 检查所选模型是否可用

### 日志查看

ComfyUI控制台会显示详细的日志信息，包括：
- 插件初始化状态
- API调用过程
- 错误信息和调试信息

*注意: 使用即梦AI服务需要遵守相关服务条款，可能产生相应费用。* 
