#!/usr/bin/env python3
"""
即梦AI视频生成工具 - 带详细进度显示版本
"""

import sys
import time
from jimeng_video_standalone import JimengVideoGenerator

class ProgressVideoGenerator(JimengVideoGenerator):
    """带进度显示的视频生成器"""
    
    def __init__(self, config_path: str = "config.json"):
        super().__init__(config_path)
        self.start_time = None
        self.last_progress = -1
    
    def generate_video_with_progress(self, 
                                   first_image_path: str,
                                   end_image_path: str, 
                                   prompt: str,
                                   duration: int = 5,
                                   model: str = "v3.0",
                                   ratio: str = "16:9"):
        """生成视频并显示详细进度"""
        
        print("🎬 即梦AI视频生成工具")
        print("=" * 50)
        
        # 显示任务信息
        print(f"📁 首帧图片: {first_image_path}")
        print(f"📁 尾帧图片: {end_image_path}")
        print(f"💭 提示词: {prompt}")
        print(f"⏱️  时长: {duration}秒")
        print(f"🤖 模型: {model}")
        print(f"📐 比例: {ratio}")
        print()
        
        # 检查余额
        print("💰 检查账号余额...")
        balance = self.get_balance()
        if balance:
            print(f"   当前余额: {balance['total_credit']} 积分")
            required_credit = 20 if model == "s2.0p" else 5
            if balance["total_credit"] < required_credit:
                print(f"❌ 积分不足，需要 {required_credit} 积分")
                return False, "积分不足"
            print(f"   将消耗: {required_credit} 积分")
        else:
            print("❌ 无法获取余额信息")
            return False, "无法获取余额"
        
        print()
        self.start_time = time.time()
        
        # 开始生成
        return super().generate_video(
            first_image_path=first_image_path,
            end_image_path=end_image_path,
            prompt=prompt,
            duration=duration,
            model=model,
            ratio=ratio
        )
    
    def _check_video_status(self, task_id: str, max_attempts: int = 200, check_interval: int = 3):
        """重写状态检查方法，添加更详细的进度显示"""
        print("🚀 开始生成视频...")
        print()
        
        try:
            attempt = 0
            last_status = None
            last_progress = -1
            status_start_time = {}
            
            while attempt < max_attempts:
                attempt += 1
                try:
                    # 获取token信息
                    timestamp = str(int(time.time()))
                    token_info = self._generate_token_info("/mweb/v1/mget_generate_task", timestamp)
                    account = self.get_current_account()
                    
                    headers = {
                        'accept': 'application/json, text/plain, */*',
                        'accept-language': 'zh-CN,zh;q=0.9',
                        'app-sdk-version': '48.0.0',
                        'appid': '513695',
                        'appvr': '5.8.0',
                        'content-type': 'application/json',
                        'cookie': token_info["cookie"],
                        'device-time': timestamp,
                        'sign': token_info["sign"],
                        'sign-ver': '1',
                        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    }
                    
                    data = {
                        "task_id_list": [task_id],
                        "http_common_info": {
                            "aid": "513695",
                            "app_id": "513695",
                            "user_id": account.get("user_id", ""),
                            "device_id": token_info["web_id"]
                        }
                    }
                    
                    response = self._make_request_with_retry(
                        "https://jimeng.jianying.com/mweb/v1/mget_generate_task?aid=513695",
                        headers, data
                    )
                    
                    if not response:
                        time.sleep(check_interval)
                        continue
                    
                    result = response.json()
                    if result.get("ret") != "0":
                        time.sleep(check_interval)
                        continue
                    
                    task_map = result.get("data", {}).get("task_map", {})
                    task_info = task_map.get(task_id)
                    
                    if not task_info:
                        time.sleep(check_interval)
                        continue
                    
                    status = task_info.get("status", 0)
                    
                    # 记录状态开始时间
                    if status != last_status:
                        status_start_time[status] = time.time()
                    
                    # 获取进度信息
                    progress_info = self._get_detailed_progress(task_info, status, status_start_time.get(status, time.time()))
                    
                    # 显示进度
                    self._display_progress(status, progress_info, last_status != status)
                    
                    if status == 50:  # 成功
                        print("\n")
                        print("✅ 视频生成完成！")
                        
                        item_list = task_info.get("item_list", [])
                        if not item_list:
                            return False, "视频生成成功但未返回URL"
                        
                        video = item_list[0].get("video", {})
                        if not video:
                            return False, "视频数据为空"
                        
                        video_info = video.get("transcoded_video", {}).get("origin", {})
                        if not video_info:
                            return False, "未获取到视频信息"
                        
                        video_url = video_info.get("video_url")
                        if not video_url:
                            return False, "未获取到视频URL"
                        
                        # 显示完成信息
                        total_time = time.time() - self.start_time
                        file_size = video_info.get('size', 0)
                        print(f"📊 生成统计:")
                        print(f"   总耗时: {total_time:.1f}秒")
                        print(f"   文件大小: {file_size / 1024 / 1024:.2f}MB")
                        print(f"   任务ID: {task_id}")
                        
                        return True, video_url
                    
                    elif status == 60:  # 失败
                        print("\n❌ 视频生成失败")
                        fail_msg = task_info.get("fail_msg", "")
                        if fail_msg:
                            print(f"   错误信息: {fail_msg}")
                        return False, f"视频生成失败: {fail_msg}"
                    
                    last_status = status
                    time.sleep(check_interval)
                    
                except Exception as e:
                    print(f"\n⚠️  检查状态时出错: {e}")
                    time.sleep(check_interval)
            
            print("\n❌ 视频生成超时")
            return False, "视频生成超时，请稍后重试"
            
        except Exception as e:
            print(f"\n❌ 检查视频状态时出错: {e}")
            return False, str(e)
    
    def _make_request_with_retry(self, url, headers, data, max_retries=3):
        """带重试的请求"""
        import requests
        
        for retry in range(max_retries):
            try:
                response = requests.post(url, headers=headers, json=data, timeout=10)
                if response.status_code == 200:
                    return response
            except Exception as e:
                if retry == max_retries - 1:
                    print(f"\n⚠️  网络请求失败: {e}")
                time.sleep(1)
        return None
    
    def _get_detailed_progress(self, task_info, status, status_start_time):
        """获取详细进度信息"""
        current_time = time.time()
        elapsed_in_status = current_time - status_start_time
        total_elapsed = current_time - self.start_time
        
        progress_info = {
            "percentage": 0.0,
            "status_time": elapsed_in_status,
            "total_time": total_elapsed,
            "estimated_remaining": 0
        }
        
        if status == 0:  # 初始化中
            progress_info["percentage"] = 2.0
            progress_info["estimated_remaining"] = 90
        elif status == 10:  # 排队中
            progress_info["percentage"] = 5.0 + min(elapsed_in_status / 30.0 * 5.0, 5.0)
            progress_info["estimated_remaining"] = 80
        elif status == 20:  # 准备中
            progress_info["percentage"] = 10.0 + min(elapsed_in_status / 20.0 * 10.0, 10.0)
            progress_info["estimated_remaining"] = 70
        elif status == 30:  # 生成中
            base_progress = 20.0
            # 生成阶段通常需要60-90秒
            time_progress = min(elapsed_in_status / 75.0 * 70.0, 70.0)
            progress_info["percentage"] = base_progress + time_progress
            remaining = max(75 - elapsed_in_status, 0)
            progress_info["estimated_remaining"] = remaining
        elif status == 40:  # 处理中
            progress_info["percentage"] = 90.0 + min(elapsed_in_status / 10.0 * 8.0, 8.0)
            progress_info["estimated_remaining"] = max(10 - elapsed_in_status, 0)
        elif status == 50:  # 生成成功
            progress_info["percentage"] = 100.0
            progress_info["estimated_remaining"] = 0
        
        return progress_info
    
    def _display_progress(self, status, progress_info, status_changed):
        """显示进度信息"""
        status_names = {
            0: "🔄 初始化中",
            10: "⏳ 排队中",
            20: "🛠️  准备中", 
            30: "🎨 生成中",
            40: "⚙️  处理中",
            50: "✅ 生成成功",
            60: "❌ 生成失败"
        }
        
        status_name = status_names.get(status, f"未知状态({status})")
        percentage = progress_info["percentage"]
        
        # 创建进度条
        bar_width = 40
        filled_width = int(bar_width * percentage / 100)
        bar = "█" * filled_width + "░" * (bar_width - filled_width)
        
        # 格式化时间
        total_time = progress_info["total_time"]
        remaining_time = progress_info["estimated_remaining"]
        
        # 显示进度行
        progress_line = f"\r{status_name} [{bar}] {percentage:.1f}% | "
        progress_line += f"已用时: {total_time:.0f}s"
        if remaining_time > 0:
            progress_line += f" | 预计剩余: {remaining_time:.0f}s"
        
        print(progress_line, end="", flush=True)
        
        # 如果状态改变，换行
        if status_changed and status != 0:
            print()


def main():
    """主函数"""
    if len(sys.argv) < 4:
        print("使用方法:")
        print("python jimeng_video_with_progress.py <首帧图片> <尾帧图片> <提示词> [时长] [模型] [比例]")
        print()
        print("示例:")
        print('python jimeng_video_with_progress.py first.jpg end.jpg "炫酷过渡效果" 5 v3.0 16:9')
        return
    
    first_image = sys.argv[1]
    end_image = sys.argv[2]
    prompt = sys.argv[3]
    duration = int(sys.argv[4]) if len(sys.argv) > 4 else 5
    model = sys.argv[5] if len(sys.argv) > 5 else "v3.0"
    ratio = sys.argv[6] if len(sys.argv) > 6 else "16:9"
    
    try:
        generator = ProgressVideoGenerator()
        success, result = generator.generate_video_with_progress(
            first_image_path=first_image,
            end_image_path=end_image,
            prompt=prompt,
            duration=duration,
            model=model,
            ratio=ratio
        )
        
        if success:
            print(f"\n🎉 视频链接: {result}")
        else:
            print(f"\n💥 生成失败: {result}")
            
    except KeyboardInterrupt:
        print("\n\n⏹️  用户中断操作")
    except Exception as e:
        print(f"\n💥 程序出错: {e}")


if __name__ == "__main__":
    main()
