# 即梦AI视频生成工具 - 文件清单

## 📁 项目结构

```
即梦AI视频生成工具/
├── 核心文件/
│   ├── jimeng_video_standalone.py     # ⭐ 主程序（独立版本）
│   ├── jimeng_video_simple.py         # ⭐ 简洁版本（推荐）
│   └── config.json                    # ⭐ 配置文件（必需）
│
├── 辅助工具/
│   ├── jimeng_video_with_progress.py  # 带虚假进度版本（不推荐）
│   ├── test_standalone.py             # 测试脚本
│   ├── example_usage.py               # 使用示例
│   └── run_video_generation.bat       # Windows批处理脚本
│
├── 文档/
│   ├── README_standalone.md           # 详细使用说明
│   └── 项目文件清单.md               # 本文件
│
└── 临时文件/（程序运行时自动创建）
    ├── temp/                          # 临时图片目录
    ├── jimeng_video.log              # 日志文件
    └── generated_video.mp4           # 生成的视频文件
```

## ⭐ 最小运行环境

如果您只想要最简单的运行环境，只需要这3个文件：

### 1. 主程序文件
- `jimeng_video_simple.py` （推荐）或 `jimeng_video_standalone.py`

### 2. 配置文件
- `config.json`

### 3. Python依赖
```bash
pip install requests pillow
```

## 📝 config.json 配置文件模板

```json
{
    "accounts": [
        {
            "sessionid": "your_sessionid_here",
            "description": "主账号"
        }
    ],
    "video_models": {
        "v3.0": {
            "name": "视频 3.0",
            "model_req_key": "dreamina_ic_generate_video_model_vgfm_3.0",
            "benefit_type": "basic_video_operation_vgfm_v_three"
        },
        "s2.0p": {
            "name": "视频 S2.0 Pro",
            "model_req_key": "dreamina_ic_generate_video_model_vgfm1.0",
            "benefit_type": "basic_video_operation_vgfm"
        },
        "s2.0": {
            "name": "视频 S2.0",
            "model_req_key": "dreamina_ic_generate_video_model_vgfm_lite",
            "benefit_type": "basic_video_operation_vgfm_lite"
        }
    },
    "video_ratios": {
        "16:9": {"width": 1024, "height": 576},
        "9:16": {"width": 576, "height": 1024},
        "1:1": {"width": 1024, "height": 1024}
    },
    "default_video_model": "v3.0",
    "default_video_ratio": "16:9"
}
```

## 🚀 快速开始

### 方法1：使用简洁版本（推荐）
```bash
# 1. 确保有Python环境
python --version

# 2. 安装依赖
pip install requests pillow

# 3. 下载文件
# - jimeng_video_simple.py
# - config.json（填入您的sessionid）

# 4. 运行
python jimeng_video_simple.py first.jpg end.jpg "提示词"
```

### 方法2：使用完整版本
```bash
# 1-3步同上

# 4. 运行
python jimeng_video_standalone.py -f first.jpg -e end.jpg -p "提示词"
```

## 📦 打包建议

如果您要分发给其他人使用，建议打包这些文件：

### 基础包（最小）
```
基础包/
├── jimeng_video_simple.py
├── config.json.template
└── README.txt
```

### 完整包
```
完整包/
├── jimeng_video_standalone.py
├── jimeng_video_simple.py
├── config.json.template
├── test_standalone.py
├── example_usage.py
├── run_video_generation.bat
├── README_standalone.md
└── requirements.txt
```

## 📋 requirements.txt
```
requests>=2.25.0
Pillow>=8.0.0
```

## ⚠️ 注意事项

### 必需配置
1. **sessionid**: 必须在config.json中填入有效的sessionid
2. **网络连接**: 需要能访问即梦AI的服务器
3. **Python版本**: 建议Python 3.7+

### 可选文件
- `test_standalone.py` - 用于测试功能是否正常
- `example_usage.py` - 代码使用示例
- `run_video_generation.bat` - Windows用户的图形界面
- `README_standalone.md` - 详细文档

### 自动生成的文件
- `temp/` - 临时图片存储目录
- `jimeng_video.log` - 运行日志
- `*.mp4` - 生成的视频文件

## 🔧 故障排除

### 常见问题
1. **ModuleNotFoundError**: 运行 `pip install requests pillow`
2. **配置文件错误**: 检查config.json格式和sessionid
3. **网络错误**: 检查网络连接和防火墙设置
4. **积分不足**: 使用 `--balance` 参数查看余额

### 获取sessionid方法
1. 访问 https://jimeng.jianying.com/
2. 登录账号
3. 按F12打开开发者工具
4. 切换到Network标签
5. 刷新页面，在请求中找到Cookie
6. 复制sessionid的值

## 📞 支持

如果遇到问题，请检查：
1. Python版本是否兼容
2. 依赖包是否正确安装
3. config.json配置是否正确
4. 网络连接是否正常
5. sessionid是否有效
