#!/usr/bin/env python3
"""
即梦AI视频生成独立脚本
支持传入两张图片和提示词，生成5秒的视频，并且能获取余额
"""

import os
import json
import logging
import time
import requests
import argparse
import sys
from pathlib import Path
from typing import Dict, Any, Optional, Tuple
from PIL import Image

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('jimeng_video.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class JimengVideoGenerator:
    """即梦AI视频生成器"""
    
    def __init__(self, config_path: str = "config.json"):
        """
        初始化视频生成器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.config = self._load_config()
        self.accounts = self.config.get("accounts", [])
        self.current_account_index = 0
        
        if not self.accounts:
            raise ValueError("配置文件中没有找到账号信息")
            
        logger.info(f"初始化完成，共加载 {len(self.accounts)} 个账号")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            if not os.path.exists(self.config_path):
                # 创建默认配置文件
                default_config = {
                    "accounts": [
                        {
                            "sessionid": "your_sessionid_here",
                            "description": "主账号"
                        }
                    ],
                    "video_models": {
                        "v3.0": {
                            "name": "视频 3.0",
                            "model_req_key": "dreamina_ic_generate_video_model_vgfm_3.0",
                            "benefit_type": "basic_video_operation_vgfm_v_three"
                        },
                        "s2.0p": {
                            "name": "视频 S2.0 Pro", 
                            "model_req_key": "dreamina_ic_generate_video_model_vgfm1.0",
                            "benefit_type": "basic_video_operation_vgfm"
                        },
                        "s2.0": {
                            "name": "视频 S2.0",
                            "model_req_key": "dreamina_ic_generate_video_model_vgfm_lite",
                            "benefit_type": "basic_video_operation_vgfm_lite"
                        }
                    },
                    "video_ratios": {
                        "16:9": {"width": 1024, "height": 576},
                        "9:16": {"width": 576, "height": 1024},
                        "1:1": {"width": 1024, "height": 1024}
                    },
                    "default_video_model": "v3.0",
                    "default_video_ratio": "16:9"
                }
                
                with open(self.config_path, 'w', encoding='utf-8') as f:
                    json.dump(default_config, f, ensure_ascii=False, indent=4)
                logger.info(f"已创建默认配置文件: {self.config_path}")
                logger.info("请在配置文件中填入您的sessionid")
                
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            logger.info("配置文件加载成功")
            return config
        except Exception as e:
            logger.error(f"配置文件加载失败: {e}")
            return {}
    
    def get_current_account(self) -> Optional[Dict[str, Any]]:
        """获取当前账号"""
        if not self.accounts:
            return None
        return self.accounts[self.current_account_index]
    
    def get_balance(self) -> Optional[Dict[str, Any]]:
        """
        获取账号余额
        
        Returns:
            dict: 包含余额信息的字典，格式为:
            {
                "gift_credit": int,      # 赠送积分
                "purchase_credit": int,  # 购买积分  
                "vip_credit": int,       # VIP积分
                "total_credit": int      # 总积分
            }
        """
        try:
            account = self.get_current_account()
            if not account:
                logger.error("没有可用的账号")
                return None
                
            url = "https://jimeng.jianying.com/commerce/v1/benefits/user_credit"
            
            # 生成请求参数
            timestamp = str(int(time.time()))
            token_info = self._generate_token_info("/commerce/v1/benefits/user_credit", timestamp)
            
            params = {
                "aid": "513695",
                "device_platform": "web", 
                "region": "CN"
            }
            
            headers = {
                'accept': 'application/json, text/plain, */*',
                'accept-language': 'zh-CN,zh;q=0.9',
                'app-sdk-version': '5.8.0',
                'appid': '513695',
                'appvr': '5.8.0',
                'content-type': 'application/json',
                'cookie': token_info["cookie"],
                'device-time': timestamp,
                'sign': token_info["sign"],
                'sign-ver': '1',
                'pf': '7',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36',
                'msToken': token_info["msToken"],
                'x-bogus': token_info["a_bogus"]
            }
            
            response = requests.post(url, headers=headers, params=params, json={})
            result = response.json()
            
            if result.get("ret") == "0" and result.get("data"):
                credit_data = result["data"]["credit"]
                balance_info = {
                    "gift_credit": credit_data["gift_credit"],
                    "purchase_credit": credit_data["purchase_credit"], 
                    "vip_credit": credit_data["vip_credit"],
                    "total_credit": credit_data["gift_credit"] + credit_data["purchase_credit"] + credit_data["vip_credit"]
                }
                logger.info(f"账号余额: 赠送积分={balance_info['gift_credit']}, 购买积分={balance_info['purchase_credit']}, VIP积分={balance_info['vip_credit']}, 总积分={balance_info['total_credit']}")
                return balance_info
            else:
                logger.error(f"获取余额失败: {result}")
                return None
                
        except Exception as e:
            logger.error(f"获取余额时出错: {e}")
            return None
    
    def generate_video(self, 
                      first_image_path: str,
                      end_image_path: str, 
                      prompt: str,
                      duration: int = 5,
                      model: str = "v3.0",
                      ratio: str = "16:9") -> Tuple[bool, str]:
        """
        生成视频
        
        Args:
            first_image_path: 首帧图片路径
            end_image_path: 尾帧图片路径
            prompt: 提示词
            duration: 视频时长（秒），默认5秒
            model: 视频模型，默认v3.0
            ratio: 视频比例，默认16:9
            
        Returns:
            tuple: (是否成功, 结果信息)
        """
        try:
            # 验证输入参数
            if not os.path.exists(first_image_path):
                return False, f"首帧图片不存在: {first_image_path}"
            if not os.path.exists(end_image_path):
                return False, f"尾帧图片不存在: {end_image_path}"
            if not prompt.strip():
                return False, "提示词不能为空"
                
            # 检查账号余额
            balance = self.get_balance()
            if not balance:
                return False, "无法获取账号余额"
                
            required_credit = 20 if model == "s2.0p" else 5
            if balance["total_credit"] < required_credit:
                return False, f"积分不足，当前积分: {balance['total_credit']}, 需要积分: {required_credit}"
            
            logger.info(f"开始生成视频，当前积分: {balance['total_credit']}")
            logger.info(f"参数: 模型={model}, 比例={ratio}, 时长={duration}秒")
            logger.info(f"提示词: {prompt}")
            
            # 首尾帧视频目前仅支持v3.0模型
            if model != "v3.0":
                return False, "首尾帧视频目前仅支持v3.0模型"
            
            # 上传图片并生成视频
            success, result = self._upload_images_and_generate(
                first_image_path, end_image_path, prompt, duration * 1000, model
            )
            
            if success:
                logger.info(f"视频生成成功: {result}")
                return True, result
            else:
                logger.error(f"视频生成失败: {result}")
                return False, result
                
        except Exception as e:
            logger.error(f"生成视频时出错: {e}")
            return False, str(e)

    def _generate_token_info(self, api_path: str, timestamp: str) -> Dict[str, str]:
        """生成token信息"""
        import random
        import hashlib

        account = self.get_current_account()
        sessionid = account.get("sessionid", "")

        # 生成web_id
        web_id = ''.join([str(random.randint(0, 9)) for _ in range(19)])

        # 生成msToken
        chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
        msToken = ''.join(random.choice(chars) for _ in range(107))

        # 生成sign
        sign_str = f"9e2c|{api_path[-7:]}|7|5.8.0|{timestamp}||11ac"
        sign = hashlib.md5(sign_str.encode()).hexdigest()

        # 生成a_bogus
        a_bogus = ''.join(random.choice(chars) for _ in range(32))

        # 生成cookie
        cookie_parts = [
            f"sessionid={sessionid}",
            f"sessionid_ss={sessionid}",
            f"_tea_web_id={web_id}",
            f"web_id={web_id}",
            f"_v2_spipe_web_id={web_id}"
        ]
        cookie = "; ".join(cookie_parts)

        return {
            "cookie": cookie,
            "msToken": msToken,
            "sign": sign,
            "a_bogus": a_bogus,
            "web_id": web_id
        }

    def _upload_images_and_generate(self, first_image_path: str, end_image_path: str,
                                   prompt: str, duration_ms: int, model: str) -> Tuple[bool, str]:
        """上传图片并生成视频"""
        try:
            # 获取上传token
            upload_token = self._get_upload_token()
            if not upload_token:
                return False, "获取上传token失败"

            # 上传首帧图片
            first_image_info = self._upload_single_image(first_image_path, upload_token, "first")
            if not first_image_info:
                return False, "上传首帧图片失败"

            # 上传尾帧图片
            end_image_info = self._upload_single_image(end_image_path, upload_token, "end")
            if not end_image_info:
                return False, "上传尾帧图片失败"

            # 生成视频
            return self._generate_frames_video(first_image_info, end_image_info, prompt, duration_ms, model)

        except Exception as e:
            logger.error(f"上传图片并生成视频时出错: {e}")
            return False, str(e)

    def _get_upload_token(self) -> Optional[Dict[str, str]]:
        """获取上传token"""
        try:
            url = "https://jimeng.jianying.com/mweb/v1/get_upload_token"
            timestamp = str(int(time.time()))
            token_info = self._generate_token_info("/mweb/v1/get_upload_token", timestamp)

            params = {
                "aid": "513695",
                "device_platform": "web",
                "region": "CN"
            }

            headers = {
                'accept': 'application/json, text/plain, */*',
                'accept-language': 'zh-CN,zh;q=0.9',
                'app-sdk-version': '48.0.0',
                'appid': '513695',
                'appvr': '5.8.0',
                'content-type': 'application/json',
                'cookie': token_info["cookie"],
                'device-time': timestamp,
                'sign': token_info["sign"],
                'sign-ver': '1',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'msToken': token_info["msToken"],
                'x-bogus': token_info["a_bogus"]
            }

            data = {"scene": 2}

            response = requests.post(url, headers=headers, params=params, json=data)
            result = response.json()

            if result.get("ret") == "0" and result.get("data"):
                token_data = result["data"]
                return {
                    "access_key_id": token_data["access_key_id"],
                    "secret_access_key": token_data["secret_access_key"],
                    "session_token": token_data["session_token"],
                    "space_name": token_data["space_name"],
                    "upload_domain": token_data["upload_domain"]
                }
            else:
                logger.error(f"获取上传token失败: {result}")
                return None

        except Exception as e:
            logger.error(f"获取上传token时出错: {e}")
            return None

    def _upload_single_image(self, image_path: str, upload_token: Dict[str, str],
                           frame_type: str) -> Optional[Dict[str, Any]]:
        """上传单张图片"""
        try:
            import datetime
            import urllib.parse
            import hashlib
            import hmac
            import binascii

            # 获取文件大小
            file_size = os.path.getsize(image_path)

            # 申请上传
            upload_info = self._apply_image_upload(upload_token, file_size)
            if not upload_info or "Result" not in upload_info:
                logger.error(f"申请{frame_type}帧图片上传失败: {upload_info}")
                return None

            logger.info(f"{frame_type}帧图片上传申请成功")

            # 上传文件
            file_response = self._upload_image_file(upload_info, image_path)
            if not file_response:
                logger.error(f"上传{frame_type}帧图片文件失败")
                return None

            logger.info(f"{frame_type}帧图片文件上传成功")

            # 提交上传
            commit_response = self._commit_image_upload(upload_token, upload_info)
            if not commit_response or "Result" not in commit_response:
                logger.error(f"提交{frame_type}帧图片上传失败: {commit_response}")
                return None

            logger.info(f"{frame_type}帧图片上传完成")
            return commit_response

        except Exception as e:
            logger.error(f"上传{frame_type}帧图片时出错: {e}")
            return None

    def _apply_image_upload(self, token_info: Dict[str, str], file_size: int) -> Optional[Dict[str, Any]]:
        """申请图片上传"""
        try:
            import datetime
            import urllib.parse
            import hashlib
            import hmac

            # 获取当前时间
            t = datetime.datetime.utcnow()
            amz_date = t.strftime('%Y%m%dT%H%M%SZ')

            # 请求参数
            request_parameters = {
                'Action': 'ApplyImageUpload',
                'FileSize': str(file_size),
                'ServiceId': token_info['space_name'],
                'Version': '2018-08-01'
            }

            # 构建规范请求字符串
            canonical_querystring = '&'.join([f'{k}={urllib.parse.quote(str(v))}' for k, v in sorted(request_parameters.items())])

            # 构建规范请求
            canonical_uri = '/'
            canonical_headers = (
                f'host:imagex.bytedanceapi.com\n'
                f'x-amz-date:{amz_date}\n'
                f'x-amz-security-token:{token_info["session_token"]}\n'
            )
            signed_headers = 'host;x-amz-date;x-amz-security-token'

            # 计算请求体哈希
            payload_hash = hashlib.sha256(b'').hexdigest()

            # 构建规范请求
            canonical_request = '\n'.join([
                'GET',
                canonical_uri,
                canonical_querystring,
                canonical_headers,
                signed_headers,
                payload_hash
            ])

            # 获取授权头
            authorization = self._get_authorization(
                token_info['access_key_id'],
                token_info['secret_access_key'],
                'cn-north-1',
                'imagex',
                amz_date,
                token_info['session_token'],
                signed_headers,
                canonical_request
            )

            # 设置请求头
            headers = {
                'Authorization': authorization,
                'X-Amz-Date': amz_date,
                'X-Amz-Security-Token': token_info['session_token'],
                'Host': 'imagex.bytedanceapi.com'
            }

            url = f'https://imagex.bytedanceapi.com/?{canonical_querystring}'

            response = requests.get(url, headers=headers)
            return response.json()

        except Exception as e:
            logger.error(f"申请图片上传时出错: {e}")
            return None

    def _upload_image_file(self, upload_info: Dict[str, Any], image_path: str) -> Optional[Dict[str, Any]]:
        """上传图片文件"""
        try:
            import binascii

            store_info = upload_info['Result']['UploadAddress']['StoreInfos'][0]
            upload_host = upload_info['Result']['UploadAddress']['UploadHosts'][0]

            url = f"https://{upload_host}/upload/v1/{store_info['StoreUri']}"

            # 计算文件的CRC32
            with open(image_path, 'rb') as f:
                content = f.read()
                crc32 = format(binascii.crc32(content) & 0xFFFFFFFF, '08x')

            headers = {
                'accept': '*/*',
                'authorization': store_info['Auth'],
                'content-type': 'application/octet-stream',
                'content-disposition': 'attachment; filename="undefined"',
                'content-crc32': crc32,
                'origin': 'https://jimeng.jianying.com',
                'referer': 'https://jimeng.jianying.com/'
            }

            response = requests.post(url, headers=headers, data=content)
            return response.json()

        except Exception as e:
            logger.error(f"上传图片文件时出错: {e}")
            return None

    def _commit_image_upload(self, token_info: Dict[str, str], upload_info: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """提交图片上传"""
        try:
            import hashlib

            amz_date = time.strftime("%Y%m%dT%H%M%SZ", time.gmtime())
            session_key = upload_info['Result']['UploadAddress']['SessionKey']

            url = f"https://{token_info['upload_domain']}"
            params = {
                "Action": "CommitImageUpload",
                "Version": "2018-08-01",
                "ServiceId": token_info['space_name']
            }

            data = {"SessionKey": session_key}
            payload = json.dumps(data)
            content_sha256 = hashlib.sha256(payload.encode('utf-8')).hexdigest()

            # 构建规范请求
            canonical_uri = "/"
            canonical_querystring = "&".join([f"{k}={v}" for k, v in sorted(params.items())])
            signed_headers = "x-amz-content-sha256;x-amz-date;x-amz-security-token"
            canonical_headers = f"x-amz-content-sha256:{content_sha256}\nx-amz-date:{amz_date}\nx-amz-security-token:{token_info['session_token']}\n"

            canonical_request = f"POST\n{canonical_uri}\n{canonical_querystring}\n{canonical_headers}\n{signed_headers}\n{content_sha256}"

            authorization = self._get_authorization(
                token_info['access_key_id'],
                token_info['secret_access_key'],
                'cn-north-1',
                'imagex',
                amz_date,
                token_info['session_token'],
                signed_headers,
                canonical_request
            )

            headers = {
                'accept': '*/*',
                'content-type': 'application/json',
                'authorization': authorization,
                'x-amz-content-sha256': content_sha256,
                'x-amz-date': amz_date,
                'x-amz-security-token': token_info['session_token'],
                'origin': 'https://jimeng.jianying.com',
                'referer': 'https://jimeng.jianying.com/'
            }

            response = requests.post(f"{url}?{canonical_querystring}", headers=headers, data=payload)
            return response.json()

        except Exception as e:
            logger.error(f"提交图片上传时出错: {e}")
            return None

    def _get_authorization(self, access_key: str, secret_key: str, region: str, service: str,
                          amz_date: str, security_token: str, signed_headers: str, canonical_request: str) -> str:
        """生成AWS授权头"""
        import hashlib
        import hmac

        algorithm = 'AWS4-HMAC-SHA256'
        datestamp = amz_date[:8]
        credential_scope = f"{datestamp}/{region}/{service}/aws4_request"

        # Create string to sign
        string_to_sign = '\n'.join([
            algorithm,
            amz_date,
            credential_scope,
            hashlib.sha256(canonical_request.encode('utf-8')).hexdigest()
        ])

        # Calculate signature
        k_date = self._sign(('AWS4' + secret_key).encode('utf-8'), datestamp)
        k_region = self._sign(k_date, region)
        k_service = self._sign(k_region, service)
        k_signing = self._sign(k_service, 'aws4_request')
        signature = hmac.new(k_signing, string_to_sign.encode('utf-8'), hashlib.sha256).hexdigest()

        # Create authorization header
        authorization_header = (
            f"{algorithm} "
            f"Credential={access_key}/{credential_scope}, "
            f"SignedHeaders={signed_headers}, "
            f"Signature={signature}"
        )

        return authorization_header

    def _sign(self, key: bytes, msg: str) -> bytes:
        """计算HMAC-SHA256签名"""
        import hmac
        import hashlib
        return hmac.new(key, msg.encode('utf-8'), hashlib.sha256).digest()
