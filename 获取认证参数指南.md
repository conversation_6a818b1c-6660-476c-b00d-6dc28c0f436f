# 即梦AI 认证参数获取指南

## 问题说明

当前插件遇到 `ret=1015` 错误，这是即梦AI的反爬虫机制触发的结果。需要获取真实的浏览器认证参数。

## 解决方案

### 步骤1：打开浏览器开发者工具

1. 使用Chrome或Edge浏览器访问：https://jimeng.jianying.com
2. 按 `F12` 打开开发者工具
3. 切换到 `Network（网络）` 标签页
4. 清空网络日志（点击禁止符号🚫）

### 步骤2：登录并生成图片

1. 登录你的即梦AI账号
2. 尝试生成一张图片（任意提示词）
3. 在网络标签页中找到生成请求

### 步骤3：找到关键请求

在网络请求列表中找到以下请求：
- URL包含：`/mweb/v1/aigc_draft/generate`
- 方法：`POST`
- 状态：`200`

### 步骤4：复制认证参数

点击该请求，查看以下信息：

#### A. 请求参数中的 web_id
在 `Query String Parameters` 中找到：
```
web_id: 5821410930714374744
```

#### B. 请求头中的 Cookie
在 `Request Headers` 中找到：
```
cookie: sessionid=你的sessionid
```

### 步骤5：更新配置文件

将获取的参数填入 `config.json`：

```json
{
    "accounts": [
        {
            "sessionid": "你的sessionid",
            "description": "主账号"
        }
    ]
}
```

## 注意事项

1. **参数时效性**：这些认证参数可能会过期，如果再次出现1015错误，需要重新获取
2. **账号安全**：请不要分享你的认证参数给他人
3. **定期更新**：建议定期更新认证参数以确保稳定性

## 故障排除

### 如果仍然出现1015错误：
1. 确认复制的参数完整且正确
2. 尝试重新登录浏览器并获取新的参数
3. 检查网络连接和防火墙设置
4. 确认即梦AI账号状态正常

### 如果出现其他错误：
- `ret=1001`: 账号未登录或token失效
- `ret=1003`: 积分不足
- `ret=1016`: 请求频率过高，需要等待

## 技术原理

即梦AI使用了复杂的反爬虫机制：
- `sessionid`: 用户会话标识
- `web_id`: 浏览器指纹标识  
- `sign/msToken/a-bogus`: 动态签名参数
- 完整的Cookie链：维持会话状态

通过提供真实的浏览器认证参数，可以绕过反爬虫检测。 