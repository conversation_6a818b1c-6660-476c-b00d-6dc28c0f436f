#!/usr/bin/env python3
"""
即梦AI视频生成工具 - 简洁版本
只显示API真实返回的状态，不做虚假的进度计算
"""

import sys
import time
from jimeng_video_standalone import JimengVideoGenerator

class SimpleVideoGenerator(JimengVideoGenerator):
    """简洁版视频生成器"""
    
    def __init__(self, config_path: str = "config.json"):
        super().__init__(config_path)
        self.start_time = None
    
    def generate_video_simple(self, 
                            first_image_path: str,
                            end_image_path: str, 
                            prompt: str,
                            duration: int = 5,
                            model: str = "v3.0",
                            ratio: str = "16:9"):
        """生成视频并显示简洁状态"""
        
        print("🎬 即梦AI视频生成")
        print("-" * 40)
        
        # 显示基本信息
        print(f"提示词: {prompt}")
        print(f"时长: {duration}秒 | 模型: {model} | 比例: {ratio}")
        
        # 检查余额
        balance = self.get_balance()
        if balance:
            required_credit = 20 if model == "s2.0p" else 5
            print(f"余额: {balance['total_credit']} 积分 (将消耗 {required_credit} 积分)")
            
            if balance["total_credit"] < required_credit:
                print("❌ 积分不足")
                return False, "积分不足"
        else:
            print("⚠️  无法获取余额")
        
        print()
        self.start_time = time.time()
        
        # 开始生成
        return super().generate_video(
            first_image_path=first_image_path,
            end_image_path=end_image_path,
            prompt=prompt,
            duration=duration,
            model=model,
            ratio=ratio
        )
    
    def _check_video_status(self, task_id: str, max_attempts: int = 200, check_interval: int = 3):
        """重写状态检查方法，只显示真实状态"""
        print("🚀 开始生成...")
        
        try:
            attempt = 0
            last_status = None
            status_start_times = {}
            
            while attempt < max_attempts:
                attempt += 1
                try:
                    # 获取token信息
                    timestamp = str(int(time.time()))
                    token_info = self._generate_token_info("/mweb/v1/mget_generate_task", timestamp)
                    account = self.get_current_account()
                    
                    headers = {
                        'accept': 'application/json, text/plain, */*',
                        'accept-language': 'zh-CN,zh;q=0.9',
                        'app-sdk-version': '48.0.0',
                        'appid': '513695',
                        'appvr': '5.8.0',
                        'content-type': 'application/json',
                        'cookie': token_info["cookie"],
                        'device-time': timestamp,
                        'sign': token_info["sign"],
                        'sign-ver': '1',
                        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    }
                    
                    data = {
                        "task_id_list": [task_id],
                        "http_common_info": {
                            "aid": "513695",
                            "app_id": "513695",
                            "user_id": account.get("user_id", ""),
                            "device_id": token_info["web_id"]
                        }
                    }
                    
                    import requests
                    response = requests.post(
                        f"https://jimeng.jianying.com/mweb/v1/mget_generate_task?aid=513695",
                        headers=headers,
                        json=data,
                        timeout=10
                    )
                    
                    if response.status_code != 200:
                        time.sleep(check_interval)
                        continue
                    
                    result = response.json()
                    if result.get("ret") != "0":
                        time.sleep(check_interval)
                        continue
                    
                    task_map = result.get("data", {}).get("task_map", {})
                    task_info = task_map.get(task_id)
                    
                    if not task_info:
                        time.sleep(check_interval)
                        continue
                    
                    status = task_info.get("status", 0)
                    
                    # 记录状态开始时间
                    if status != last_status:
                        status_start_times[status] = time.time()
                        
                        # 显示状态变化
                        status_names = {
                            0: "🔄 初始化中",
                            10: "⏳ 排队中",
                            20: "🛠️  准备中", 
                            30: "🎨 生成中",
                            40: "⚙️  处理中",
                            50: "✅ 生成成功",
                            60: "❌ 生成失败"
                        }
                        
                        status_name = status_names.get(status, f"未知状态({status})")
                        elapsed = time.time() - self.start_time
                        print(f"{status_name} (总耗时: {elapsed:.0f}s)")
                        
                        # 如果有API返回的真实进度信息，显示它
                        task_payload = task_info.get("task_payload", {})
                        if "progress" in task_payload:
                            real_progress = task_payload.get("progress")
                            print(f"   API返回进度: {real_progress}")
                        elif "percent" in task_payload:
                            real_progress = task_payload.get("percent")
                            print(f"   API返回进度: {real_progress}%")
                    
                    if status == 50:  # 成功
                        item_list = task_info.get("item_list", [])
                        if not item_list:
                            return False, "视频生成成功但未返回URL"
                        
                        video = item_list[0].get("video", {})
                        if not video:
                            return False, "视频数据为空"
                        
                        video_info = video.get("transcoded_video", {}).get("origin", {})
                        if not video_info:
                            return False, "未获取到视频信息"
                        
                        video_url = video_info.get("video_url")
                        if not video_url:
                            return False, "未获取到视频URL"
                        
                        # 显示完成信息
                        total_time = time.time() - self.start_time
                        file_size = video_info.get('size', 0)
                        print(f"\n📊 生成完成:")
                        print(f"   总耗时: {total_time:.1f}秒")
                        print(f"   文件大小: {file_size / 1024 / 1024:.2f}MB")
                        
                        return True, video_url
                    
                    elif status == 60:  # 失败
                        fail_msg = task_info.get("fail_msg", "")
                        error_msg = f"视频生成失败"
                        if fail_msg:
                            error_msg += f": {fail_msg}"
                        return False, error_msg
                    
                    last_status = status
                    time.sleep(check_interval)
                    
                except Exception as e:
                    print(f"⚠️  检查状态出错: {e}")
                    time.sleep(check_interval)
            
            return False, "视频生成超时"
            
        except Exception as e:
            return False, str(e)


def main():
    """主函数"""
    if len(sys.argv) < 4:
        print("使用方法:")
        print("python jimeng_video_simple.py <首帧图片> <尾帧图片> <提示词> [时长] [模型] [比例]")
        print()
        print("示例:")
        print('python jimeng_video_simple.py first.jpg end.jpg "炫酷过渡效果" 5 v3.0 16:9')
        return
    
    first_image = sys.argv[1]
    end_image = sys.argv[2]
    prompt = sys.argv[3]
    duration = int(sys.argv[4]) if len(sys.argv) > 4 else 5
    model = sys.argv[5] if len(sys.argv) > 5 else "v3.0"
    ratio = sys.argv[6] if len(sys.argv) > 6 else "16:9"
    
    try:
        generator = SimpleVideoGenerator()
        success, result = generator.generate_video_simple(
            first_image_path=first_image,
            end_image_path=end_image,
            prompt=prompt,
            duration=duration,
            model=model,
            ratio=ratio
        )
        
        if success:
            print(f"\n🎉 视频生成成功!")
            print(f"🔗 链接: {result}")
        else:
            print(f"\n💥 生成失败: {result}")
            
    except KeyboardInterrupt:
        print("\n⏹️  用户中断")
    except Exception as e:
        print(f"\n💥 出错: {e}")


if __name__ == "__main__":
    main()
