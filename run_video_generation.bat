@echo off
chcp 65001 >nul
echo 即梦AI视频生成工具
echo ==================

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

REM 检查依赖是否安装
python -c "import requests, PIL" >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖...
    pip install requests pillow
    if errorlevel 1 (
        echo 错误: 依赖安装失败
        pause
        exit /b 1
    )
)

REM 检查配置文件
if not exist "config.json" (
    echo 首次运行，正在创建配置文件...
    python jimeng_video_standalone.py --balance >nul 2>&1
    echo.
    echo 配置文件已创建: config.json
    echo 请编辑config.json文件，填入您的sessionid
    echo.
    echo 如何获取sessionid:
    echo 1. 访问 https://jimeng.jianying.com/
    echo 2. 登录您的账号
    echo 3. 按F12打开开发者工具
    echo 4. 切换到Network标签
    echo 5. 刷新页面，在请求中找到Cookie
    echo 6. 复制sessionid的值到config.json中
    echo.
    pause
    exit /b 0
)

:menu
echo.
echo 请选择操作:
echo 1. 查看账号余额
echo 2. 生成视频
echo 3. 运行测试
echo 4. 退出
echo.
set /p choice=请输入选择 (1-4): 

if "%choice%"=="1" goto balance
if "%choice%"=="2" goto generate
if "%choice%"=="3" goto test
if "%choice%"=="4" goto exit
echo 无效选择，请重新输入
goto menu

:balance
echo.
echo 正在查询账号余额...
python jimeng_video_standalone.py --balance
pause
goto menu

:generate
echo.
echo 视频生成向导
echo =============
set /p first_image=请输入首帧图片路径: 
set /p end_image=请输入尾帧图片路径: 
set /p prompt=请输入提示词: 
set /p duration=请输入视频时长(秒，默认5): 
set /p model=请输入模型(v3.0/s2.0p/s2.0，默认v3.0): 
set /p ratio=请输入比例(16:9/9:16/1:1，默认16:9): 
set /p output=请输入输出文件路径(可选): 

REM 设置默认值
if "%duration%"=="" set duration=5
if "%model%"=="" set model=v3.0
if "%ratio%"=="" set ratio=16:9

REM 构建命令
set cmd=python jimeng_video_standalone.py -f "%first_image%" -e "%end_image%" -p "%prompt%" -d %duration% -m %model% -r %ratio%
if not "%output%"=="" set cmd=%cmd% -o "%output%"

echo.
echo 正在生成视频...
echo 命令: %cmd%
echo.
%cmd%
pause
goto menu

:test
echo.
echo 正在运行测试...
python test_standalone.py
pause
goto menu

:exit
echo 再见！
exit /b 0
