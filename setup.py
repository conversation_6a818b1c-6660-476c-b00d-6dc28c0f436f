#!/usr/bin/env python3
"""
即梦AI视频生成工具 - 安装和配置脚本
"""

import os
import sys
import json
import subprocess

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 7):
        print("❌ 需要Python 3.7或更高版本")
        print(f"   当前版本: {sys.version}")
        return False
    print(f"✅ Python版本检查通过: {sys.version.split()[0]}")
    return True

def install_dependencies():
    """安装依赖包"""
    print("\n📦 安装依赖包...")
    
    try:
        # 检查是否已安装
        import requests
        import PIL
        print("✅ 依赖包已安装")
        return True
    except ImportError:
        pass
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "requests", "pillow"])
        print("✅ 依赖包安装成功")
        return True
    except subprocess.CalledProcessError:
        print("❌ 依赖包安装失败")
        print("   请手动运行: pip install requests pillow")
        return False

def create_config_file():
    """创建配置文件"""
    print("\n⚙️  配置文件设置...")
    
    if os.path.exists("config.json"):
        print("✅ config.json 已存在")
        return True
    
    if os.path.exists("config.json.template"):
        print("📋 从模板创建配置文件...")
        try:
            with open("config.json.template", "r", encoding="utf-8") as f:
                template = f.read()
            
            with open("config.json", "w", encoding="utf-8") as f:
                f.write(template)
            
            print("✅ 配置文件创建成功: config.json")
            print("⚠️  请编辑 config.json 文件，填入您的sessionid")
            return True
        except Exception as e:
            print(f"❌ 创建配置文件失败: {e}")
            return False
    else:
        print("❌ 未找到配置文件模板")
        return False

def test_installation():
    """测试安装"""
    print("\n🧪 测试安装...")
    
    try:
        # 测试导入
        from jimeng_video_standalone import JimengVideoGenerator
        
        # 测试初始化
        generator = JimengVideoGenerator()
        
        if not generator.accounts:
            print("⚠️  配置文件中没有账号信息，请填入sessionid")
            return False
        
        if generator.accounts[0].get("sessionid") == "your_sessionid_here":
            print("⚠️  请在config.json中填入真实的sessionid")
            return False
        
        print("✅ 安装测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 安装测试失败: {e}")
        return False

def show_usage_guide():
    """显示使用指南"""
    print("\n📖 使用指南")
    print("=" * 50)
    
    print("\n1️⃣  获取sessionid:")
    print("   - 访问 https://jimeng.jianying.com/")
    print("   - 登录您的账号")
    print("   - 按F12打开开发者工具")
    print("   - 切换到Network标签")
    print("   - 刷新页面，在请求中找到Cookie")
    print("   - 复制sessionid的值到config.json")
    
    print("\n2️⃣  基本使用:")
    print("   python jimeng_video_simple.py first.jpg end.jpg '提示词'")
    
    print("\n3️⃣  完整参数:")
    print("   python jimeng_video_standalone.py \\")
    print("     --first-image first.jpg \\")
    print("     --end-image end.jpg \\")
    print("     --prompt '提示词' \\")
    print("     --duration 5 \\")
    print("     --model v3.0 \\")
    print("     --ratio 16:9 \\")
    print("     --output video.mp4")
    
    print("\n4️⃣  查看余额:")
    print("   python jimeng_video_standalone.py --balance")
    
    print("\n5️⃣  运行测试:")
    print("   python test_standalone.py")

def main():
    """主函数"""
    print("🎬 即梦AI视频生成工具 - 安装向导")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        return
    
    # 安装依赖
    if not install_dependencies():
        return
    
    # 创建配置文件
    if not create_config_file():
        return
    
    # 测试安装
    test_result = test_installation()
    
    # 显示使用指南
    show_usage_guide()
    
    print("\n" + "=" * 50)
    if test_result:
        print("🎉 安装完成！您可以开始使用了。")
    else:
        print("⚠️  安装完成，但需要配置sessionid才能使用。")
    
    print("\n📁 项目文件:")
    files = [
        "jimeng_video_standalone.py",
        "jimeng_video_simple.py", 
        "config.json",
        "test_standalone.py"
    ]
    
    for file in files:
        if os.path.exists(file):
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file} (缺失)")

if __name__ == "__main__":
    main()
