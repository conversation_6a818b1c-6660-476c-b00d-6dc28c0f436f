#!/usr/bin/env python3
"""
即梦AI视频生成独立脚本测试文件
"""

import os
import sys
from PIL import Image
import tempfile

def create_test_images():
    """创建测试用的图片"""
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    
    # 创建首帧图片（蓝色背景）
    first_image = Image.new('RGB', (1024, 576), color='blue')
    first_path = os.path.join(temp_dir, 'first_frame.jpg')
    first_image.save(first_path, 'JPEG')
    
    # 创建尾帧图片（红色背景）
    end_image = Image.new('RGB', (1024, 576), color='red')
    end_path = os.path.join(temp_dir, 'end_frame.jpg')
    end_image.save(end_path, 'JPEG')
    
    print(f"测试图片已创建:")
    print(f"首帧图片: {first_path}")
    print(f"尾帧图片: {end_path}")
    
    return first_path, end_path

def test_balance():
    """测试余额查询功能"""
    print("\n=== 测试余额查询 ===")
    
    try:
        from jimeng_video_standalone import JimengVideoGenerator
        
        generator = JimengVideoGenerator()
        balance = generator.get_balance()
        
        if balance:
            print("✅ 余额查询成功")
            print(f"总积分: {balance['total_credit']}")
            return True
        else:
            print("❌ 余额查询失败")
            return False
            
    except Exception as e:
        print(f"❌ 余额查询出错: {e}")
        return False

def test_video_generation():
    """测试视频生成功能"""
    print("\n=== 测试视频生成 ===")
    
    try:
        from jimeng_video_standalone import JimengVideoGenerator
        
        # 创建测试图片
        first_path, end_path = create_test_images()
        
        # 初始化生成器
        generator = JimengVideoGenerator()
        
        # 测试视频生成
        success, result = generator.generate_video(
            first_image_path=first_path,
            end_image_path=end_path,
            prompt="从蓝色渐变到红色的过渡效果",
            duration=5,
            model="v3.0",
            ratio="16:9"
        )
        
        if success:
            print("✅ 视频生成成功")
            print(f"视频链接: {result}")
            return True
        else:
            print(f"❌ 视频生成失败: {result}")
            return False
            
    except Exception as e:
        print(f"❌ 视频生成出错: {e}")
        return False
    finally:
        # 清理测试图片
        try:
            os.unlink(first_path)
            os.unlink(end_path)
            os.rmdir(os.path.dirname(first_path))
        except:
            pass

def test_config_creation():
    """测试配置文件创建"""
    print("\n=== 测试配置文件创建 ===")
    
    try:
        from jimeng_video_standalone import JimengVideoGenerator
        
        # 使用不存在的配置文件路径
        test_config_path = "test_config.json"
        
        # 确保测试配置文件不存在
        if os.path.exists(test_config_path):
            os.unlink(test_config_path)
        
        # 初始化生成器，应该会创建配置文件
        generator = JimengVideoGenerator(test_config_path)
        
        if os.path.exists(test_config_path):
            print("✅ 配置文件创建成功")
            
            # 清理测试配置文件
            os.unlink(test_config_path)
            return True
        else:
            print("❌ 配置文件创建失败")
            return False
            
    except Exception as e:
        print(f"❌ 配置文件创建出错: {e}")
        return False

def main():
    """主测试函数"""
    print("即梦AI视频生成独立脚本测试")
    print("=" * 50)
    
    # 检查依赖
    try:
        import requests
        import PIL
        print("✅ 依赖检查通过")
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请运行: pip install requests pillow")
        return
    
    # 测试配置文件创建
    config_test = test_config_creation()
    
    # 检查是否有有效的配置文件
    if not os.path.exists("config.json"):
        print("\n⚠️  未找到config.json配置文件")
        print("请先运行脚本创建配置文件，并填入有效的sessionid")
        return
    
    # 测试余额查询
    balance_test = test_balance()
    
    # 如果余额查询成功，继续测试视频生成
    if balance_test:
        video_test = test_video_generation()
    else:
        print("\n⚠️  余额查询失败，跳过视频生成测试")
        print("请检查config.json中的sessionid是否正确")
        video_test = False
    
    # 测试结果汇总
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    print(f"配置文件创建: {'✅ 通过' if config_test else '❌ 失败'}")
    print(f"余额查询: {'✅ 通过' if balance_test else '❌ 失败'}")
    print(f"视频生成: {'✅ 通过' if video_test else '❌ 失败'}")
    
    if all([config_test, balance_test, video_test]):
        print("\n🎉 所有测试通过！脚本可以正常使用。")
    else:
        print("\n⚠️  部分测试失败，请检查配置和网络连接。")

if __name__ == "__main__":
    main()
