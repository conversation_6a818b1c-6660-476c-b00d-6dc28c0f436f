#!/usr/bin/env python3
"""
即梦AI视频生成独立脚本使用示例
"""

import os
import sys
from jimeng_video_standalone import JimengVideoGenerator

def example_1_basic_usage():
    """示例1: 基本使用方法"""
    print("=== 示例1: 基本使用方法 ===")
    
    try:
        # 初始化生成器
        generator = JimengVideoGenerator("config.json")
        
        # 查看余额
        balance = generator.get_balance()
        if balance:
            print(f"当前余额: {balance['total_credit']} 积分")
        
        # 生成视频
        success, result = generator.generate_video(
            first_image_path="images/start.jpg",
            end_image_path="images/end.jpg", 
            prompt="从白天到黑夜的城市变化",
            duration=5,
            model="v3.0",
            ratio="16:9"
        )
        
        if success:
            print(f"✅ 视频生成成功: {result}")
        else:
            print(f"❌ 视频生成失败: {result}")
            
    except Exception as e:
        print(f"❌ 出错: {e}")

def example_2_batch_generation():
    """示例2: 批量生成视频"""
    print("\n=== 示例2: 批量生成视频 ===")
    
    # 定义视频任务列表
    video_tasks = [
        {
            "first_image": "images/scene1_start.jpg",
            "end_image": "images/scene1_end.jpg",
            "prompt": "日出时分的山峰景色",
            "output": "videos/sunrise.mp4"
        },
        {
            "first_image": "images/scene2_start.jpg", 
            "end_image": "images/scene2_end.jpg",
            "prompt": "海浪拍打岩石的动态效果",
            "output": "videos/ocean.mp4"
        },
        {
            "first_image": "images/scene3_start.jpg",
            "end_image": "images/scene3_end.jpg", 
            "prompt": "森林中阳光透过树叶的变化",
            "output": "videos/forest.mp4"
        }
    ]
    
    try:
        generator = JimengVideoGenerator()
        
        # 检查余额
        balance = generator.get_balance()
        if not balance:
            print("❌ 无法获取余额信息")
            return
            
        required_credits = len(video_tasks) * 5  # 假设每个视频需要5积分
        if balance['total_credit'] < required_credits:
            print(f"❌ 积分不足，需要{required_credits}积分，当前{balance['total_credit']}积分")
            return
        
        print(f"开始批量生成 {len(video_tasks)} 个视频...")
        
        success_count = 0
        for i, task in enumerate(video_tasks, 1):
            print(f"\n处理任务 {i}/{len(video_tasks)}: {task['prompt']}")
            
            # 检查图片是否存在
            if not os.path.exists(task['first_image']):
                print(f"❌ 首帧图片不存在: {task['first_image']}")
                continue
            if not os.path.exists(task['end_image']):
                print(f"❌ 尾帧图片不存在: {task['end_image']}")
                continue
            
            # 生成视频
            success, result = generator.generate_video(
                first_image_path=task['first_image'],
                end_image_path=task['end_image'],
                prompt=task['prompt'],
                duration=5,
                model="v3.0",
                ratio="16:9"
            )
            
            if success:
                print(f"✅ 视频生成成功: {result}")
                
                # 下载视频
                if 'output' in task:
                    try:
                        import requests
                        response = requests.get(result, stream=True)
                        response.raise_for_status()
                        
                        # 确保输出目录存在
                        os.makedirs(os.path.dirname(task['output']), exist_ok=True)
                        
                        with open(task['output'], 'wb') as f:
                            for chunk in response.iter_content(chunk_size=8192):
                                f.write(chunk)
                        
                        print(f"✅ 视频已下载: {task['output']}")
                        success_count += 1
                    except Exception as e:
                        print(f"❌ 下载失败: {e}")
            else:
                print(f"❌ 视频生成失败: {result}")
        
        print(f"\n批量生成完成，成功: {success_count}/{len(video_tasks)}")
        
        # 显示剩余余额
        final_balance = generator.get_balance()
        if final_balance:
            print(f"剩余余额: {final_balance['total_credit']} 积分")
            
    except Exception as e:
        print(f"❌ 批量生成出错: {e}")

def example_3_custom_config():
    """示例3: 使用自定义配置"""
    print("\n=== 示例3: 使用自定义配置 ===")
    
    try:
        # 使用自定义配置文件
        generator = JimengVideoGenerator("custom_config.json")
        
        # 检查配置是否加载成功
        if not generator.accounts:
            print("❌ 配置文件中没有账号信息")
            return
        
        print(f"已加载 {len(generator.accounts)} 个账号")
        
        # 查看余额
        balance = generator.get_balance()
        if balance:
            print(f"当前账号余额: {balance['total_credit']} 积分")
        
        # 生成不同比例的视频
        ratios = ["16:9", "9:16", "1:1"]
        
        for ratio in ratios:
            print(f"\n生成 {ratio} 比例的视频...")
            
            success, result = generator.generate_video(
                first_image_path="test_images/start.jpg",
                end_image_path="test_images/end.jpg",
                prompt=f"适合{ratio}比例的动态场景",
                duration=5,
                model="v3.0",
                ratio=ratio
            )
            
            if success:
                print(f"✅ {ratio} 视频生成成功: {result}")
            else:
                print(f"❌ {ratio} 视频生成失败: {result}")
                
    except Exception as e:
        print(f"❌ 自定义配置示例出错: {e}")

def example_4_error_handling():
    """示例4: 错误处理和重试机制"""
    print("\n=== 示例4: 错误处理和重试机制 ===")
    
    def generate_with_retry(generator, max_retries=3, **kwargs):
        """带重试机制的视频生成"""
        for attempt in range(max_retries):
            try:
                print(f"尝试 {attempt + 1}/{max_retries}...")
                success, result = generator.generate_video(**kwargs)
                
                if success:
                    return True, result
                else:
                    print(f"生成失败: {result}")
                    if attempt < max_retries - 1:
                        print("等待10秒后重试...")
                        import time
                        time.sleep(10)
                    
            except Exception as e:
                print(f"出现异常: {e}")
                if attempt < max_retries - 1:
                    print("等待10秒后重试...")
                    import time
                    time.sleep(10)
        
        return False, "重试次数已用完"
    
    try:
        generator = JimengVideoGenerator()
        
        # 使用重试机制生成视频
        success, result = generate_with_retry(
            generator,
            max_retries=3,
            first_image_path="images/start.jpg",
            end_image_path="images/end.jpg",
            prompt="测试重试机制的视频生成",
            duration=5,
            model="v3.0",
            ratio="16:9"
        )
        
        if success:
            print(f"✅ 最终生成成功: {result}")
        else:
            print(f"❌ 最终生成失败: {result}")
            
    except Exception as e:
        print(f"❌ 错误处理示例出错: {e}")

def main():
    """主函数"""
    print("即梦AI视频生成独立脚本使用示例")
    print("=" * 50)
    
    # 检查配置文件
    if not os.path.exists("config.json"):
        print("❌ 未找到config.json配置文件")
        print("请先运行 jimeng_video_standalone.py 创建配置文件")
        return
    
    # 运行示例
    try:
        example_1_basic_usage()
        # example_2_batch_generation()  # 需要相应的图片文件
        # example_3_custom_config()     # 需要自定义配置文件
        # example_4_error_handling()    # 需要相应的图片文件
        
    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"❌ 程序出错: {e}")

if __name__ == "__main__":
    main()
