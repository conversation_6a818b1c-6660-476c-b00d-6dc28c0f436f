# 即梦AI视频生成独立脚本

这是一个独立运行的Python脚本，可以使用即梦AI生成首尾帧视频，支持传入两张图片和提示词，生成5秒的视频，并且能获取余额。

## 功能特点

- ✅ 支持首尾帧视频生成（需要两张图片）
- ✅ 支持自定义提示词
- ✅ 支持多种视频模型（v3.0, s2.0p, s2.0）
- ✅ 支持多种视频比例（16:9, 9:16, 1:1）
- ✅ 支持查看账号余额
- ✅ 支持自动下载生成的视频
- ✅ 详细的日志记录

## 安装依赖

```bash
pip install requests pillow
```

## 配置文件

首次运行时，脚本会自动创建 `config.json` 配置文件，你需要填入你的sessionid：

```json
{
    "accounts": [
        {
            "sessionid": "your_sessionid_here",
            "description": "主账号"
        }
    ],
    "video_models": {
        "v3.0": {
            "name": "视频 3.0",
            "model_req_key": "dreamina_ic_generate_video_model_vgfm_3.0",
            "benefit_type": "basic_video_operation_vgfm_v_three"
        },
        "s2.0p": {
            "name": "视频 S2.0 Pro",
            "model_req_key": "dreamina_ic_generate_video_model_vgfm1.0",
            "benefit_type": "basic_video_operation_vgfm"
        },
        "s2.0": {
            "name": "视频 S2.0",
            "model_req_key": "dreamina_ic_generate_video_model_vgfm_lite",
            "benefit_type": "basic_video_operation_vgfm_lite"
        }
    },
    "video_ratios": {
        "16:9": {"width": 1024, "height": 576},
        "9:16": {"width": 576, "height": 1024},
        "1:1": {"width": 1024, "height": 1024}
    },
    "default_video_model": "v3.0",
    "default_video_ratio": "16:9"
}
```

### 如何获取sessionid

1. 打开浏览器，访问 [即梦AI](https://jimeng.jianying.com/)
2. 登录你的账号
3. 按F12打开开发者工具
4. 切换到 Network（网络）标签
5. 刷新页面或进行任何操作
6. 在请求中找到Cookie，复制其中的sessionid值

## 使用方法

### 基本用法

```bash
python jimeng_video_standalone.py -f first_frame.jpg -e end_frame.jpg -p "一个美丽的日落场景"
```

### 完整参数

```bash
python jimeng_video_standalone.py \
    --first-image first_frame.jpg \
    --end-image end_frame.jpg \
    --prompt "一个美丽的日落场景" \
    --duration 5 \
    --model v3.0 \
    --ratio 16:9 \
    --output output_video.mp4
```

### 查看余额

```bash
python jimeng_video_standalone.py --balance
```

## 参数说明

| 参数 | 简写 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| --first-image | -f | ✅ | - | 首帧图片路径 |
| --end-image | -e | ✅ | - | 尾帧图片路径 |
| --prompt | -p | ✅ | - | 视频生成提示词 |
| --duration | -d | ❌ | 5 | 视频时长（秒） |
| --model | -m | ❌ | v3.0 | 视频模型 |
| --ratio | -r | ❌ | 16:9 | 视频比例 |
| --config | -c | ❌ | config.json | 配置文件路径 |
| --balance | -b | ❌ | - | 仅查看账号余额 |
| --output | -o | ❌ | - | 输出视频文件路径 |

## 支持的模型

- **v3.0**: 视频 3.0（推荐，支持首尾帧视频）
- **s2.0p**: 视频 S2.0 Pro（更合理的动效）
- **s2.0**: 视频 S2.0（快速生成）

**注意**: 首尾帧视频目前仅支持v3.0模型。

## 支持的比例

- **16:9**: 横屏视频 (1024x576)
- **9:16**: 竖屏视频 (576x1024)
- **1:1**: 方形视频 (1024x1024)

## 积分消耗

- **v3.0**: 5积分/次
- **s2.0p**: 20积分/次
- **s2.0**: 5积分/次

## 使用示例

### 示例1：生成横屏视频

```bash
python jimeng_video_standalone.py \
    -f images/start.jpg \
    -e images/end.jpg \
    -p "从白天到黑夜的城市变化" \
    -d 5 \
    -m v3.0 \
    -r 16:9 \
    -o videos/city_transition.mp4
```

### 示例2：生成竖屏视频

```bash
python jimeng_video_standalone.py \
    -f portrait_start.jpg \
    -e portrait_end.jpg \
    -p "人物表情从微笑到惊讶的变化" \
    -r 9:16 \
    -o portrait_video.mp4
```

### 示例3：查看余额

```bash
python jimeng_video_standalone.py --balance
```

## 输出示例

```
当前账号余额: 50 积分

=== 开始生成视频 ===
首帧图片: first_frame.jpg
尾帧图片: end_frame.jpg
提示词: 一个美丽的日落场景
时长: 5秒
模型: v3.0
比例: 16:9

2024-01-01 12:00:00 - INFO - 开始生成视频，当前积分: 50
2024-01-01 12:00:01 - INFO - 首帧图片上传申请成功
2024-01-01 12:00:02 - INFO - 首帧图片文件上传成功
2024-01-01 12:00:03 - INFO - 首帧图片上传完成
2024-01-01 12:00:04 - INFO - 尾帧图片上传申请成功
2024-01-01 12:00:05 - INFO - 尾帧图片文件上传成功
2024-01-01 12:00:06 - INFO - 尾帧图片上传完成
2024-01-01 12:00:07 - INFO - 发送视频生成请求...
2024-01-01 12:00:08 - INFO - 视频生成任务创建成功，任务ID: abc123
2024-01-01 12:00:09 - INFO - 视频状态: 初始化中
2024-01-01 12:00:12 - INFO - 视频状态: 排队中
2024-01-01 12:00:15 - INFO - 视频状态: 生成中
2024-01-01 12:01:30 - INFO - 视频状态: 生成成功
2024-01-01 12:01:30 - INFO - 视频生成完成，大小: 2048576 bytes

✅ 视频生成成功！
视频链接: https://example.com/video.mp4

正在下载视频到: output_video.mp4
✅ 视频下载完成: output_video.mp4

生成后账号余额: 45 积分
```

## 注意事项

1. **图片格式**: 支持常见的图片格式（JPG, PNG等）
2. **图片大小**: 建议图片不要过大，以免上传失败
3. **网络连接**: 需要稳定的网络连接
4. **积分充足**: 确保账号有足够的积分
5. **模型限制**: 首尾帧视频目前仅支持v3.0模型

## 故障排除

### 常见错误

1. **配置文件错误**: 检查sessionid是否正确填写
2. **图片不存在**: 检查图片路径是否正确
3. **积分不足**: 使用 `--balance` 参数查看余额
4. **网络超时**: 检查网络连接，重试操作
5. **模型不支持**: 首尾帧视频只能使用v3.0模型

### 日志文件

程序运行时会生成 `jimeng_video.log` 日志文件，包含详细的执行信息，可用于故障排除。

## 许可证

本项目仅供学习和研究使用，请遵守即梦AI的服务条款。
